/*
 * @Descripttion:
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-08-05 11:07:41
 * @LastEditors: kongweiqiang
 * @LastEditTime: 2024-11-27 15:52:58
 */
const { BrowserWindow, ipcMain } = require('electron')
const AppContext = require('../../../packages/PreliminaryEstimate/core/container/APPContext');
const { ObjectUtil } = require('../../../common/ObjectUtil');

/**
 * 窗口插件
 * @class
 */
class WinAddon {
	constructor(app) {
		this.app = app
		this.windowContentsIdMap = {}
		this.windowIdMap = {}
	}

	/**
	 * create window
	 *
	 * @function
	 * @since 1.0.0
	 */
	create(name, opt) {
		const options = Object.assign(
			{
				width: 1366,
				height: 768,
				useContentSize: true,
				frame: false, // 去掉默认的标题栏
				show: false, // 防止白屏，在ready-to-show时显示
				transparent: false, // 启用窗口透明
				backgroundColor: '#00000000', // 透明背景色，防止白屏
				webPreferences: {
					contextIsolation: false,
					nodeIntegration: true,
					backgroundThrottling: false, // 防止后台节流影响透明效果
				},
			},
			opt
		)
		const win = new BrowserWindow(options)
		// 防止窗口闪烁，直接显示
		win.once('ready-to-show', () => {
			console.log('子窗口准备显示');
			win.show();
		})
		const winContentsId = win.webContents.id
		const id = win.id
		win.webContents.on('did-finish-load', () => {
			this.registerWCid(name, winContentsId);
			this.registerId(name, id);
		})

		win.webContents.on('destroyed', () => {
			this.removeWCid(name);
			this.removeId(id);
		})
    ipcMain.on('postMsgToChild', (event,data) => {
			this.postMsgToChild(data)
		})
		win.webContents.on('render-process-gone', (event, details) => {
			this.removeWCid(name);
			this.registerId(name, id);
		})
		ipcMain.on('window-min-child', (event, { id }) => {
			this.childMin(winContentsId, event.sender.id, id)
		})
		ipcMain.on('window-max-child', (event, { id }) => {
      this.childMax(winContentsId, event.sender.id, id)
    })
		ipcMain.on('window-close-child', (event, { id }) => {
      this.childClose(winContentsId, event.sender.id, id)
    })
		return win
	}

  /**
   * 发送消息给子窗口
   * @param {Object} data 传递的数据
   * @param {String} data.windowId 窗口id
   * @param {String} data.type 窗口type
   * @param {Object} data.props 窗口props
   * @param {Object} data.store 窗口store
   */
  postMsgToChild({windowId,type,props,store}) {
    let newVar = global.windowMap.get(`${windowId}-modal-${type}`)
    const window = BrowserWindow.fromId(newVar)
    if (window.isVisible()) {
      try {
        window.webContents.send('getProps', { props, store });
      } catch (error) {
        console.error('getProps', error);
      }
    }
  }
	/**
	 * @name: 对应子窗口最小化
	 * @msg:
	 * @params {*}
	 */
	childMin(winContentsId, childId, id) {
		if (winContentsId === childId) {
			let windowMap = ObjectUtil.isEmpty(AppContext.getWindowMap()) ? global.windowMap : AppContext.getWindowMap();
			let childWin = BrowserWindow.fromId(windowMap.get(id))
			childWin.minimize()
		}
	}
	/**
	 * @name: 对应子窗口最大、窗口化
	 * @msg:
	 * @params {*}
	 */
	childMax(winContentsId, childId, id) {
		if (winContentsId === childId) {
			let windowMap = ObjectUtil.isEmpty(AppContext.getWindowMap()) ? global.windowMap : AppContext.getWindowMap();
			let childWin = BrowserWindow.fromId(windowMap.get(id))
			if (childWin.isMaximized()) {
				childWin.unmaximize()
			} else {
				childWin.maximize()
			}
		}
	}
	/**
	 * @name: 对应子窗口关闭
	 * @msg:
	 * @params {*}
	 */
	childClose(winContentsId, childId, id) {
		if (winContentsId === childId) {
			let windowMap = ObjectUtil.isEmpty(AppContext.getWindowMap()) ? global.windowMap : AppContext.getWindowMap();

			let childWin = BrowserWindow.fromId(windowMap.get(id))
			global.windowMap.delete(id);
			childWin.destroy();

		}
	}
	/**
	 * 获取窗口Contents id
	 *
	 * @function
	 * @since 1.0.0
	 */
	getWCid(name) {
		const id = this.windowContentsIdMap[name] || null
		return id
	}

	/**
	 * 获取主窗口Contents id
	 *
	 * @function
	 * @since 1.0.0
	 */
	getMWCid() {
		const id = this.windowContentsIdMap['main'] || null
		return id
	}

	/**
	 * 注册窗口Contents id
	 *
	 * @function
	 * @since 1.0.0
	 */
	registerWCid(name, id) {
		this.windowContentsIdMap[name] = id;
	}
	registerId(name, id) {
		this.windowIdMap[id] = name;
	}

	/**
	 * 销毁窗口Contents id
	 *
	 * @function
	 * @since 1.0.0
	 */
	removeWCid(name) {
		delete this.windowContentsIdMap[name]
	}
	removeId(id) {
		delete this.windowIdMap[id]
	}
}

module.exports = WinAddon
